"""
Multi-Agent Coordinator Implementation

This coordinator manages the collaboration between RL, LLM, and CZSC agents,
implementing decision fusion and conflict resolution strategies.
"""

import logging
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

from agents import BaseAgent, TradingSignal, MarketData, RLAgent, LLMAgent, CZSCAgent


@dataclass
class AgentWeight:
    """Agent weight configuration for decision fusion"""
    rl_weight: float = 0.4      # RL agent weight (quantitative analysis)
    llm_weight: float = 0.3     # LLM agent weight (semantic analysis)
    czsc_weight: float = 0.3    # CZSC agent weight (technical analysis)
    
    def normalize(self):
        """Normalize weights to sum to 1.0"""
        total = self.rl_weight + self.llm_weight + self.czsc_weight
        if total > 0:
            self.rl_weight /= total
            self.llm_weight /= total
            self.czsc_weight /= total


@dataclass
class CoordinatedDecision:
    """Result of coordinated decision making"""
    final_action: str
    final_confidence: float
    individual_signals: Dict[str, TradingSignal]
    fusion_method: str
    conflict_resolution: str
    reasoning: str
    timestamp: datetime


class MultiAgentCoordinator:
    """
    多智能体协调器，支持多策略融合与性能统计
    """
    def __init__(self, agents: Dict[str, Any], fusion_methods: Optional[List[str]] = None):
        self.agents = agents  # {'rl': RLAgent, 'llm': LLMAgent, 'czsc': CZSCAgent}
        self.fusion_methods = fusion_methods or ["weighted", "consensus", "confidence_override", "conservative"]
        self.logger = logging.getLogger("MultiAgentCoordinator")
        self.performance_stats = defaultdict(lambda: defaultdict(list))  # {method: {agent: [correct, total]}}
        self.last_experiment_results = None

    def generate_coordinated_decision(self, market_data: Any, method: str = "weighted") -> Dict[str, Any]:
        """根据指定融合策略生成决策，并记录统计"""
        agent_signals = {}
        for name, agent in self.agents.items():
            # 假设每个agent有generate_signal方法，返回{'action': str, 'confidence': float}
            signal = agent.generate_signal(market_data)
            agent_signals[name] = signal
        # 融合决策
        final_decision = self._fuse_signals(agent_signals, method)
        # 记录性能统计（需传入真实标签时可用）
        self._update_stats(agent_signals, final_decision, method)
        return {'final_decision': final_decision, 'agent_signals': agent_signals}

    def _fuse_signals(self, agent_signals: Dict[str, Any], method: str) -> Dict[str, Any]:
        """多策略融合实现（简化版）"""
        if method == "weighted":
            # 简单加权平均（假设每个signal有confidence）
            actions = defaultdict(float)
            total_weight = 0
            for name, sig in agent_signals.items():
                actions[sig['action']] += sig.get('confidence', 1.0)
                total_weight += sig.get('confidence', 1.0)
            best_action = max(actions, key=actions.get)
            return {'action': best_action, 'confidence': actions[best_action] / (total_weight or 1)}
        elif method == "consensus":
            # 多数投票
            votes = defaultdict(int)
            for sig in agent_signals.values():
                votes[sig['action']] += 1
            best_action = max(votes, key=votes.get)
            return {'action': best_action, 'confidence': votes[best_action] / len(agent_signals)}
        elif method == "confidence_override":
            # 置信度最高者决定
            best = max(agent_signals.values(), key=lambda s: s.get('confidence', 0))
            return {'action': best['action'], 'confidence': best['confidence']}
        elif method == "conservative":
            # 只要有hold则hold，否则加权
            if any(sig['action'] == 'hold' for sig in agent_signals.values()):
                return {'action': 'hold', 'confidence': 1.0}
            return self._fuse_signals(agent_signals, 'weighted')
        else:
            return self._fuse_signals(agent_signals, 'weighted')

    def _update_stats(self, agent_signals, final_decision, method):
        # 仅结构预留，需传入真实标签时可用
        pass

    def run_fusion_experiment(self, market_data_list: List[Any], true_labels: Optional[List[str]] = None) -> Dict[str, Any]:
        """对比不同融合策略的实验接口，输出每种策略的决策分布和性能统计"""
        results = {m: {'decisions': [], 'correct': 0, 'total': 0} for m in self.fusion_methods}
        for i, data in enumerate(market_data_list):
            label = true_labels[i] if true_labels else None
            for method in self.fusion_methods:
                out = self.generate_coordinated_decision(data, method)
                action = out['final_decision']['action']
                results[method]['decisions'].append(action)
                if label is not None:
                    if action == label:
                        results[method]['correct'] += 1
                    results[method]['total'] += 1
        # 统计准确率
        for method in self.fusion_methods:
            if results[method]['total'] > 0:
                results[method]['accuracy'] = results[method]['correct'] / results[method]['total']
            else:
                results[method]['accuracy'] = None
        self.last_experiment_results = results
        return results 