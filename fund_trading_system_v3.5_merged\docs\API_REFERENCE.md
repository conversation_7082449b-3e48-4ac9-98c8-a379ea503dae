# API 参考文档 - Trading System v3.5

## 📋 概述

本文档提供Trading System v3.5的完整API参考，包括核心模块、代理接口、协调器和工具函数的详细说明。

## 🤖 智能代理 API

### BaseAgent 基类

所有智能代理的抽象基类，定义了统一的接口规范。

```python
class BaseAgent(ABC):
    """智能代理基类"""
    
    def __init__(self, name: str):
        """初始化代理
        
        Args:
            name: 代理名称
        """
    
    @abstractmethod
    def generate_signal(self, data: MarketData) -> TradingSignal:
        """生成交易信号
        
        Args:
            data: 市场数据
            
        Returns:
            TradingSignal: 交易信号对象
        """
    
    @abstractmethod
    def get_confidence(self) -> float:
        """获取当前置信度
        
        Returns:
            float: 置信度 (0.0-1.0)
        """
    
    def get_explanation(self) -> str:
        """获取决策解释
        
        Returns:
            str: 人类可读的决策解释
        """
```

### RLAgent 强化学习代理

基于深度强化学习的量化分析代理。

```python
class RLAgent(BaseAgent):
    """强化学习代理"""
    
    def __init__(self, name: str, model_path: Optional[str] = None):
        """初始化RL代理
        
        Args:
            name: 代理名称
            model_path: 预训练模型路径(可选)
        """
    
    def generate_signal(self, data: MarketData) -> TradingSignal:
        """基于RL模型生成交易信号
        
        Args:
            data: 包含技术指标的市场数据
            
        Returns:
            TradingSignal: RL分析结果
        """
    
    def load_model(self, model_path: str) -> bool:
        """加载预训练模型
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            bool: 加载是否成功
        """
```

### LLMAgent 大语言模型代理

基于大语言模型的语义分析代理。

```python
class LLMAgent(BaseAgent):
    """大语言模型代理"""
    
    def __init__(self, name: str, providers: Optional[List[LLMProviderBase]] = None):
        """初始化LLM代理
        
        Args:
            name: 代理名称
            providers: LLM提供商列表
        """
    
    def generate_signal(self, data: MarketData) -> TradingSignal:
        """基于LLM分析生成交易信号
        
        Args:
            data: 包含新闻数据的市场数据
            
        Returns:
            TradingSignal: LLM分析结果
        """
    
    def analyze_sentiment(self, text: str) -> Dict[str, float]:
        """分析文本情感
        
        Args:
            text: 待分析文本
            
        Returns:
            Dict[str, float]: 情感分析结果
        """
```

### CZSCAgent 缠中说禅代理

基于缠论的技术分析代理。

```python
class CZSCAgent(BaseAgent):
    """缠中说禅代理"""
    
    def __init__(self, name: str):
        """初始化CZSC代理
        
        Args:
            name: 代理名称
        """
    
    def generate_signal(self, data: MarketData) -> TradingSignal:
        """基于缠论分析生成交易信号
        
        Args:
            data: 包含CZSC结构的市场数据
            
        Returns:
            TradingSignal: 缠论分析结果
        """
    
    def analyze_structure(self, price_data: pd.DataFrame) -> Dict[str, Any]:
        """分析缠论结构
        
        Args:
            price_data: 价格数据
            
        Returns:
            Dict[str, Any]: 缠论结构分析结果
        """
```

## 🎛️ 协调器 API

### MultiAgentCoordinator 多代理协调器

负责融合多个代理的决策结果。

```python
class MultiAgentCoordinator:
    """多代理协调器"""
    
    def __init__(self, agent_weights: AgentWeight):
        """初始化协调器
        
        Args:
            agent_weights: 代理权重配置
        """
    
    def generate_coordinated_decision(self, data: MarketData) -> CoordinatedDecision:
        """生成协调决策
        
        Args:
            data: 市场数据
            
        Returns:
            CoordinatedDecision: 协调决策结果
        """
    
    def update_weights(self, new_weights: AgentWeight) -> None:
        """更新代理权重
        
        Args:
            new_weights: 新的权重配置
        """
    
    def get_coordination_stats(self) -> Dict[str, Any]:
        """获取协调统计信息
        
        Returns:
            Dict[str, Any]: 协调统计数据
        """
```

### TradingAgent 统一交易代理

提供统一的交易决策接口。

```python
class TradingAgent:
    """统一交易代理"""
    
    def __init__(self, weights: AgentWeight, risk_tolerance: str = "MEDIUM"):
        """初始化交易代理
        
        Args:
            weights: 代理权重
            risk_tolerance: 风险容忍度 ("LOW", "MEDIUM", "HIGH")
        """
    
    def analyze_and_recommend(self, symbol: str, price_data: Dict) -> TradingRecommendation:
        """分析并生成交易建议
        
        Args:
            symbol: 交易标的
            price_data: 价格数据
            
        Returns:
            TradingRecommendation: 交易建议
        """
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态
        
        Returns:
            Dict[str, Any]: 系统状态信息
        """
```

## 📊 数据结构 API

### MarketData 市场数据

标准化的市场数据结构。

```python
@dataclass
class MarketData:
    """市场数据结构"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    technical_indicators: Dict[str, float] = field(default_factory=dict)
    czsc_structure: Dict[str, float] = field(default_factory=dict)
    news_data: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_price_change(self) -> float:
        """计算价格变化率"""
    
    def get_volatility(self) -> float:
        """计算波动率"""
    
    def to_feature_vector(self) -> np.ndarray:
        """转换为50维特征向量"""
```

### TradingSignal 交易信号

代理生成的交易信号结构。

```python
@dataclass
class TradingSignal:
    """交易信号结构"""
    agent_type: str
    action: str  # "buy", "sell", "hold"
    strength: float  # 0.0-1.0
    confidence: float  # 0.0-1.0
    timestamp: datetime
    reasoning: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_actionable(self, min_confidence: float = 0.6) -> bool:
        """判断信号是否可执行"""
```

## ⚙️ 配置管理 API

### TradingConfig 交易配置

系统配置管理类。

```python
class TradingConfig:
    """交易配置管理"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化配置
        
        Args:
            config_path: 配置文件路径(可选)
        """
    
    def get_agent_weights(self) -> Dict[str, float]:
        """获取代理权重配置"""
    
    def get_feature_dimensions(self) -> Dict[str, int]:
        """获取特征维度配置"""
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
    
    def validate_config(self) -> Tuple[bool, List[str]]:
        """验证配置有效性"""
```

## 🔧 工具函数 API

### Logger 日志工具

```python
class Logger:
    """日志工具类"""
    
    @staticmethod
    def get_logger(name: str) -> logging.Logger:
        """获取日志记录器
        
        Args:
            name: 日志记录器名称
            
        Returns:
            logging.Logger: 配置好的日志记录器
        """
```

### DataValidator 数据验证器

```python
class DataValidator:
    """数据验证器"""
    
    def validate_market_data(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证市场数据
        
        Args:
            data: 市场数据字典
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误列表)
        """
```

### FeatureProcessor 特征处理器

```python
class FeatureProcessor:
    """特征处理器"""
    
    def calculate_technical_indicators(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算技术指标
        
        Args:
            data: 价格数据
            
        Returns:
            Dict[str, float]: 技术指标字典
        """
```

## 📈 回测系统 API

### BacktestEngine 回测引擎

```python
class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, config: TradingConfig, backtest_config: BacktestConfig):
        """初始化回测引擎"""
    
    def run_backtest(self, data: pd.DataFrame) -> BacktestResult:
        """运行回测
        
        Args:
            data: 历史数据
            
        Returns:
            BacktestResult: 回测结果
        """
```

## 🚀 使用示例

### 基础使用示例

```python
from coordinators.trading_agent import TradingAgent, AgentWeight
from agents.base_agent import MarketData
from datetime import datetime

# 创建交易代理
weights = AgentWeight(rl_weight=0.4, llm_weight=0.3, czsc_weight=0.3)
agent = TradingAgent(weights, risk_tolerance="MEDIUM")

# 创建市场数据
market_data = MarketData(
    symbol="000001.SZ",
    timestamp=datetime.now(),
    open=15.20, high=15.45, low=15.10, close=15.35,
    volume=2500000
)

# 获取交易建议
recommendation = agent.analyze_and_recommend(
    symbol="000001.SZ",
    price_data={
        'open': 15.20, 'high': 15.45, 
        'low': 15.10, 'close': 15.35, 
        'volume': 2500000
    }
)

print(f"交易建议: {recommendation.action}")
print(f"置信度: {recommendation.confidence:.2%}")
```

---

**文档版本**: v1.0  
**最后更新**: 2025-01-22  
**维护者**: Trading System Team
