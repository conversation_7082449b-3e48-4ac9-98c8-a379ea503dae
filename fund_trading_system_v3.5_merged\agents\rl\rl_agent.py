"""
Reinforcement Learning Agent Implementation

This agent uses trained RL models to generate quantitative trading signals
based on technical indicators and market microstructure data.
"""

import numpy as np
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

from agents.base_agent import BaseAgent, TradingSignal, MarketData


class RLAgent(BaseAgent):
    """
    Reinforcement Learning trading agent.
    
    Uses trained RL models (PPO/SAC/A3C) to analyze technical indicators
    and generate trading signals based on market microstructure patterns.
    """
    
    def __init__(self, name: str = "RLAgent", model_path: Optional[str] = None):
        """
        Initialize the RL Agent.
        
        Args:
            name: Agent name
            model_path: Path to trained RL model
        """
        super().__init__(name)
        self.logger = logging.getLogger(f"{self.__class__.__name__}_{name}")
        
        self.model_path = model_path
        self.model = None
        self.state_dim = 50  # 20 technical + 20 llm + 10 czsc
        self.action_dim = 3  # buy, hold, sell
        
        # RL specific configuration
        self.rl_config = {
            'algorithm': 'PPO',  # PPO, SAC, or A3C
            'state_normalization': True,
            'risk_adjustment': True,
            'confidence_threshold': 0.6
        }
        
        # Feature extraction weights for technical indicators (20-dim)
        self.technical_feature_weights = {
            # Trend indicators (6-dim)
            'ma_signal': 0.15,           # Moving average signal
            'macd_signal': 0.12,         # MACD signal  
            'adx_strength': 0.10,        # ADX trend strength
            'trend_consistency': 0.08,   # Trend consistency
            'price_position': 0.07,      # Price position
            'breakout_signal': 0.06,     # Breakout signal
            
            # Momentum indicators (5-dim)
            'rsi_signal': 0.09,          # RSI signal
            'kdj_signal': 0.08,          # KDJ signal
            'momentum_strength': 0.07,   # Momentum strength
            'williams_r': 0.05,          # Williams %R
            'cci_signal': 0.04,          # CCI signal
            
            # Volatility indicators (4-dim)
            'bollinger_position': 0.06,  # Bollinger position
            'atr_ratio': 0.05,           # ATR relative value
            'volatility_signal': 0.04,   # Volatility signal
            'vix_like': 0.03,            # VIX-like indicator
            
            # Volume-price indicators (3-dim)
            'volume_strength': 0.05,     # Volume strength
            'obv_signal': 0.04,          # OBV signal
            'price_volume_trend': 0.03,  # Price-volume trend
            
            # Support/Resistance indicators (2-dim)
            'support_resistance': 0.03,  # Support/resistance position
            'pivot_signal': 0.02         # Pivot point signal
        }
        
        self.current_confidence = 0.5
        
        # Initialize current technical indicators storage
        self.technical_indicators = {}
        
    def initialize(self) -> bool:
        """Initialize the RL model and required resources."""
        try:
            if self.model_path and Path(self.model_path).exists():
                self.model = self._load_model(self.model_path)
                self.logger.info(f"RL model loaded from {self.model_path}")
            else:
                self.logger.warning("No model path provided or model not found, using rule-based fallback")
                self.model = None
            
            self.is_initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize RL agent: {e}")
            return False
    
    def generate_signal(self, data: MarketData) -> TradingSignal:
        """
        Generate trading signal using RL model or rule-based fallback.
        
        Args:
            data: Market data including technical indicators
            
        Returns:
            TradingSignal with RL-based decision
        """
        try:
            # Extract state vector (focus on technical indicators for RL)
            state_vector = self._create_state_vector(data)
            
            if self.model is not None:
                # Use trained RL model
                action_probs = self._predict_with_model(state_vector)
                action, confidence = self._process_model_output(action_probs)
                reasoning = f"RL model prediction: {action} (confidence: {confidence:.3f})"
            else:
                # Fallback to rule-based logic
                action, confidence = self._rule_based_decision(data)
                reasoning = f"Rule-based RL decision: {action} (no trained model available)"
            
            # Update current confidence
            self.current_confidence = confidence
            
            signal = TradingSignal(
                agent_type=self.agent_type,
                action=action,
                strength=confidence,
                confidence=confidence,
                timestamp=datetime.now(),
                reasoning=reasoning,
                metadata={
                    'model_used': self.model is not None,
                    'state_vector_norm': float(np.linalg.norm(state_vector)),
                    'technical_features': self._extract_technical_features(data)
                }
            )
            
            self.logger.debug(f"Generated RL signal: {action} with confidence {confidence:.3f}")
            return signal
            
        except Exception as e:
            self.logger.error(f"Error generating RL signal: {e}")
            # Return conservative signal on error
            return TradingSignal(
                agent_type=self.agent_type,
                action="hold",
                strength=0.1,
                confidence=0.1,
                timestamp=datetime.now(),
                reasoning=f"Error in RL signal generation: {str(e)}",
                metadata={'error': str(e)}
            )
    
    def get_confidence(self) -> float:
        """Get current confidence level."""
        return self.current_confidence
    
    def get_explanation(self) -> str:
        """Get human-readable explanation of current RL analysis state"""
        if not self.model:
            return "RL Agent: 使用基于规则的回退逻辑进行决策（未加载训练模型）。"
        
        return f"RL Agent: 使用训练好的强化学习模型进行量化分析。当前置信度: {self.current_confidence:.2f}。"
    
    def _create_state_vector(self, data: MarketData) -> np.ndarray:
        """
        Create 50-dimensional state vector from market data.
        For RL agent, we focus on the technical indicators portion (20-dim).
        """
        # Extract technical features (20-dim)
        technical_features = self._extract_technical_features(data)
        
        # For RL agent, we'll use zeros for LLM and CZSC features initially
        # These will be filled by the coordination manager
        llm_features = np.zeros(20)
        czsc_features = np.zeros(10)
        
        # Combine into 50-dimensional state vector
        state_vector = np.concatenate([technical_features, llm_features, czsc_features])
        
        # Normalize if configured
        if self.rl_config['state_normalization']:
            state_vector = self._normalize_state(state_vector)
        
        return state_vector.astype(np.float32)
    
    def _extract_technical_features(self, data: MarketData) -> np.ndarray:
        """Extract 20-dimensional technical indicator features."""
        features = np.zeros(20)
        
        indicators = data.technical_indicators
        
        try:
            # Trend indicators (6-dim)
            features[0] = indicators.get('ma_signal', 0.0)
            features[1] = indicators.get('macd_signal', 0.0)
            features[2] = indicators.get('adx_strength', 0.0)
            features[3] = indicators.get('trend_consistency', 0.0)
            features[4] = indicators.get('price_position', 0.0)
            features[5] = indicators.get('breakout_signal', 0.0)
            
            # Momentum indicators (5-dim)
            features[6] = indicators.get('rsi_signal', 0.0)
            features[7] = indicators.get('kdj_signal', 0.0)
            features[8] = indicators.get('momentum_strength', 0.0)
            features[9] = indicators.get('williams_r', 0.0)
            features[10] = indicators.get('cci_signal', 0.0)
            
            # Volatility indicators (4-dim)
            features[11] = indicators.get('bollinger_position', 0.0)
            features[12] = indicators.get('atr_ratio', 0.0)
            features[13] = indicators.get('volatility_signal', 0.0)
            features[14] = indicators.get('vix_like', 0.0)
            
            # Volume-price indicators (3-dim)
            features[15] = indicators.get('volume_strength', 0.0)
            features[16] = indicators.get('obv_signal', 0.0)
            features[17] = indicators.get('price_volume_trend', 0.0)
            
            # Support/Resistance indicators (2-dim)
            features[18] = indicators.get('support_resistance', 0.0)
            features[19] = indicators.get('pivot_signal', 0.0)
            
        except Exception as e:
            self.logger.warning(f"Error extracting technical features: {e}")
            
        return features
    
    def _normalize_state(self, state_vector: np.ndarray) -> np.ndarray:
        """Normalize state vector to [-1, 1] range."""
        # Simple normalization - can be enhanced with learned statistics
        return np.tanh(state_vector)
    
    def _predict_with_model(self, state_vector: np.ndarray) -> np.ndarray:
        """Predict action probabilities using trained model."""
        # Placeholder for actual model prediction
        # In real implementation, this would call the trained RL model
        mock_probs = np.random.dirichlet([1, 1, 1])  # Mock prediction
        return mock_probs
    
    def _process_model_output(self, action_probs: np.ndarray) -> tuple[str, float]:
        """Process model output into action and confidence."""
        action_names = ['buy', 'hold', 'sell']
        
        # Get action with highest probability
        action_idx = np.argmax(action_probs)
        action = action_names[action_idx]
        
        # Confidence is the max probability
        confidence = float(action_probs[action_idx])
        
        # Apply confidence threshold
        if confidence < self.rl_config['confidence_threshold']:
            action = 'hold'
            confidence = max(0.1, confidence * 0.5)
        
        return action, confidence
    
    def _rule_based_decision(self, data: MarketData) -> tuple[str, float]:
        """Fallback rule-based decision when no model is available."""
        indicators = data.technical_indicators
        
        # Simple rule-based logic
        buy_signals = 0
        sell_signals = 0
        
        # Check trend indicators
        if indicators.get('ma_signal', 0) > 0.1:
            buy_signals += 1
        elif indicators.get('ma_signal', 0) < -0.1:
            sell_signals += 1
            
        # Check momentum
        if indicators.get('rsi_signal', 0) > 0.1:
            buy_signals += 1
        elif indicators.get('rsi_signal', 0) < -0.1:
            sell_signals += 1
        
        # Make decision
        if buy_signals > sell_signals and buy_signals >= 2:
            return 'buy', min(0.8, 0.4 + buy_signals * 0.1)
        elif sell_signals > buy_signals and sell_signals >= 2:
            return 'sell', min(0.8, 0.4 + sell_signals * 0.1)
        else:
            return 'hold', 0.3
    
    def _load_model(self, model_path: str):
        """Load trained RL model."""
        # Placeholder for model loading logic
        # In real implementation, this would load PyTorch/TensorFlow model
        self.logger.info(f"Mock loading RL model from {model_path}")
        return "mock_model" 

    def update_state(self, market_data: MarketData):
        """Update agent internal state with new market data"""
        # Update technical indicators if available
        if hasattr(market_data, 'technical_indicators') and market_data.technical_indicators:
            self.technical_indicators.update(market_data.technical_indicators)
    
    def get_features(self) -> Dict[str, Any]:
        """Get current agent features and state"""
        return {
            'model_loaded': self.model is not None,
            'initialized': self.is_initialized,
            'current_confidence': self.current_confidence,
            'technical_indicators': list(self.technical_indicators.keys())
        } 