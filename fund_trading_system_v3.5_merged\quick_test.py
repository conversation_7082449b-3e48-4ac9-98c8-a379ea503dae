#!/usr/bin/env python3
"""
Quick Test Script for Trading System v3.5

This script performs basic validation of the trading system components
to ensure everything is working correctly.
"""

import sys
import traceback
from datetime import datetime

def test_imports():
    """Test all critical imports"""
    print("🔍 Testing imports...")
    
    try:
        # Core imports
        from core.config import TradingConfig
        from core.data_structures import PerformanceMetrics
        from core.utils import Logger, DataValidator, FeatureProcessor
        print("   ✅ Core modules imported successfully")
        
        # Agent imports
        from agents.base_agent import BaseAgent, TradingSignal, MarketData
        from agents.rl.rl_agent import RLAgent
        from agents.llm.llm_agent import LLMAgent
        from agents.czsc.czsc_agent import CZSCAgent
        print("   ✅ Agent modules imported successfully")
        
        # Coordinator imports
        from coordinators.multi_agent_coordinator import MultiAgentCoordinator, AgentWeight
        from coordinators.trading_agent import TradingAgent
        print("   ✅ Coordinator modules imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False

def test_configuration():
    """Test configuration system"""
    print("\n⚙️ Testing configuration...")
    
    try:
        from core.config import TradingConfig
        
        # Test default configuration
        config = TradingConfig()
        print("   ✅ Default configuration loaded")
        
        # Test configuration summary
        summary = config.get_config_summary()
        print(f"   ✅ Configuration summary: {len(summary)} sections")
        
        # Test agent weights
        weights = config.get_agent_weights()
        total_weight = sum(weights.values())
        print(f"   ✅ Agent weights sum to {total_weight:.3f}")
        
        # Test feature dimensions
        dimensions = config.get_feature_dimensions()
        total_dims = dimensions['total']
        print(f"   ✅ Feature dimensions: {total_dims} total (20+10+20)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration error: {e}")
        traceback.print_exc()
        return False

def test_agents():
    """Test individual agents"""
    print("\n🤖 Testing agents...")
    
    try:
        from agents.rl.rl_agent import RLAgent
        from agents.llm.llm_agent import LLMAgent
        from agents.czsc.czsc_agent import CZSCAgent
        from agents.base_agent import MarketData
        
        # Create test market data
        test_data = MarketData(
            symbol="TEST",
            timestamp=datetime.now(),
            open=100.0,
            high=102.0,
            low=99.0,
            close=101.0,
            volume=1000000,
            technical_indicators={f'tech_{i}': 0.5 for i in range(20)},
            czsc_structure={f'czsc_{i}': 0.0 for i in range(10)},
            news_data=[],
            metadata={}
        )
        
        # Test RL Agent
        rl_agent = RLAgent("TestRL")
        rl_signal = rl_agent.generate_signal(test_data)
        print(f"   ✅ RL Agent: {rl_signal.action} (confidence: {rl_signal.confidence:.2f})")
        
        # Test LLM Agent
        llm_agent = LLMAgent("TestLLM")
        llm_signal = llm_agent.generate_signal(test_data)
        print(f"   ✅ LLM Agent: {llm_signal.action} (confidence: {llm_signal.confidence:.2f})")
        
        # Test CZSC Agent
        czsc_agent = CZSCAgent("TestCZSC")
        czsc_signal = czsc_agent.generate_signal(test_data)
        print(f"   ✅ CZSC Agent: {czsc_signal.action} (confidence: {czsc_signal.confidence:.2f})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Agent error: {e}")
        traceback.print_exc()
        return False

def test_coordinator():
    """Test multi-agent coordination"""
    print("\n🎯 Testing coordination...")
    
    try:
        from coordinators.multi_agent_coordinator import MultiAgentCoordinator, AgentWeight
        from agents.base_agent import MarketData
        
        # Create test market data
        test_data = MarketData(
            symbol="TEST",
            timestamp=datetime.now(),
            open=100.0,
            high=102.0,
            low=99.0,
            close=101.0,
            volume=1000000,
            technical_indicators={f'tech_{i}': 0.5 for i in range(20)},
            czsc_structure={f'czsc_{i}': 0.0 for i in range(10)},
            news_data=[],
            metadata={}
        )
        
        # Test coordinator
        weights = AgentWeight(rl_weight=0.4, llm_weight=0.3, czsc_weight=0.3)
        coordinator = MultiAgentCoordinator(weights)
        
        decision = coordinator.generate_coordinated_decision(test_data)
        print(f"   ✅ Coordinated decision: {decision.final_action} (confidence: {decision.final_confidence:.2f})")
        print(f"   ✅ Fusion method: {decision.fusion_method}")
        
        # Test coordination stats
        stats = coordinator.get_coordination_stats()
        print(f"   ✅ Coordination stats available: {len(stats)} metrics")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Coordination error: {e}")
        traceback.print_exc()
        return False

def test_trading_agent():
    """Test unified trading agent interface"""
    print("\n💼 Testing trading agent...")
    
    try:
        from coordinators.trading_agent import TradingAgent, AgentWeight
        
        # Create trading agent
        weights = AgentWeight(rl_weight=0.4, llm_weight=0.3, czsc_weight=0.3)
        agent = TradingAgent(weights, risk_tolerance="MEDIUM")
        
        # Test recommendation
        test_price_data = {
            'symbol': 'TEST',
            'timestamp': datetime.now(),
            'open': 100.0,
            'high': 102.0,
            'low': 99.0,
            'close': 101.0,
            'volume': 1000000
        }
        
        recommendation = agent.analyze_and_recommend("TEST", test_price_data)
        print(f"   ✅ Recommendation: {recommendation.action} (confidence: {recommendation.confidence:.2f})")
        print(f"   ✅ Risk level: {recommendation.risk_level}")
        
        # Test system status
        status = agent.get_system_status()
        print(f"   ✅ System status: {status.get('status', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Trading agent error: {e}")
        traceback.print_exc()
        return False

def test_data_structures():
    """Test core data structures"""
    print("\n📊 Testing data structures...")
    
    try:
        from core.data_structures import PerformanceMetrics
        from agents.base_agent import MarketData, TradingSignal
        
        # Test MarketData
        market_data = MarketData(
            symbol="TEST",
            timestamp=datetime.now(),
            open=100.0,
            high=102.0,
            low=99.0,
            close=101.0,
            volume=1000000,
            technical_indicators={f'tech_{i}': 0.5 for i in range(20)},
            czsc_structure={f'czsc_{i}': 0.0 for i in range(10)}
        )
        
        price_change = market_data.get_price_change()
        volatility = market_data.get_volatility()
        feature_vector = market_data.to_feature_vector()
        
        print(f"   ✅ MarketData: Price change {price_change:.2%}, Volatility {volatility:.2%}")
        print(f"   ✅ Feature vector: {feature_vector.shape} dimensions")
        
        # Test TradingSignal
        signal = TradingSignal(
            agent_type="test",
            action="buy",
            strength=0.75,
            confidence=0.75,
            timestamp=datetime.now(),
            reasoning="Test signal",
            metadata={'symbol': 'TEST'}
        )
        
        print(f"   ✅ TradingSignal: {signal.action} with {signal.confidence:.1%} confidence")
        print(f"   ✅ Signal actionable: True")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Data structure error: {e}")
        traceback.print_exc()
        return False

def test_utilities():
    """Test utility functions"""
    print("\n🛠️ Testing utilities...")
    
    try:
        from core.utils import Logger, DataValidator, FeatureProcessor
        import pandas as pd
        import numpy as np
        
        # Test Logger
        logger = Logger.get_logger("TestLogger")
        logger.info("Test log message")
        print("   ✅ Logger working")
        
        # Test DataValidator
        validator = DataValidator()
        
        test_market_data = {
            'symbol': 'TEST',
            'timestamp': datetime.now(),
            'open': 100.0,
            'high': 102.0,
            'low': 99.0,
            'close': 101.0,
            'volume': 1000000
        }
        
        is_valid, errors = validator.validate_market_data(test_market_data)
        print(f"   ✅ Data validation: {'Valid' if is_valid else f'Invalid ({errors})'}")
        
        # Test FeatureProcessor
        processor = FeatureProcessor()
        
        # Create sample price data
        dates = pd.date_range(start='2023-01-01', periods=30)
        sample_data = pd.DataFrame({
            'close': np.random.normal(100, 2, 30).cumsum() + 100,
            'high': np.random.normal(102, 2, 30).cumsum() + 102,
            'low': np.random.normal(98, 2, 30).cumsum() + 98,
            'volume': np.random.normal(1000000, 100000, 30)
        }, index=dates)
        
        indicators = processor.calculate_technical_indicators(sample_data)
        print(f"   ✅ Technical indicators: {len(indicators)} calculated")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Utilities error: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Trading System v3.5 - Quick Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configuration),
        ("Agent Test", test_agents),
        ("Coordinator Test", test_coordinator),
        ("Trading Agent Test", test_trading_agent),
        ("Data Structure Test", test_data_structures),
        ("Utilities Test", test_utilities)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Trading System v3.5 is ready to use.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 