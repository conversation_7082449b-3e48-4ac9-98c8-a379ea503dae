"""
Data Manager for Backtesting

Handles loading, preprocessing, and validation of historical market data
for the backtesting engine.
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import os

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False

try:
    import tushare as ts
    TUSHARE_AVAILABLE = True
except ImportError:
    TUSHARE_AVAILABLE = False


class DataManager:
    """
    Data Manager for Backtesting Engine
    
    Handles loading, validation, and preprocessing of historical market data
    from various sources (CSV files, databases, APIs).
    """
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize the data manager.
        
        Args:
            data_dir: Directory containing historical data files
        """
        self.logger = logging.getLogger("DataManager")
        self.data_dir = Path(data_dir)
        self.data_cache = {}
        
        # Create data directory if it doesn't exist
        self.data_dir.mkdir(exist_ok=True)
        
        # Standard column mapping for different data sources
        self.column_mapping = {
            'Open': 'open',
            'High': 'high', 
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'Date': 'date',
            'Symbol': 'symbol',
            'Adj Close': 'adj_close'
        }
        
        self.logger.info(f"Data manager initialized with data directory: {self.data_dir}")
    
    def load_data(self, 
                  symbol: str,
                  start_date: Optional[str] = None,
                  end_date: Optional[str] = None,
                  source: str = "csv",
                  tushare_token: Optional[str] = None) -> pd.DataFrame:
        """
        Load historical market data for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., "000001.SZ")
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            source: Data source ("csv", "database", "api")
            tushare_token: tushare数据接口token（可选，若source为tushare时必需）
            
        Returns:
            DataFrame with standardized OHLCV data
        """
        try:
            cache_key = f"{symbol}_{start_date}_{end_date}_{source}"
            
            # Check cache first
            if cache_key in self.data_cache:
                self.logger.debug(f"Loading {symbol} from cache")
                return self.data_cache[cache_key].copy()
            
            # Load data based on source
            if source == "csv":
                data = self._load_csv_data(symbol, start_date, end_date)
            elif source == "akshare":
                data = self._load_akshare_data(symbol, start_date, end_date)
            elif source == "tushare":
                data = self._load_tushare_data(symbol, start_date, end_date, tushare_token)
            elif source == "database":
                data = self._load_database_data(symbol, start_date, end_date)
            elif source == "api":
                data = self._load_api_data(symbol, start_date, end_date)
            else:
                raise ValueError(f"Unsupported data source: {source}")
            
            # Validate and standardize data
            data = self._validate_and_standardize(data, symbol)
            
            # Filter by date range if specified
            if start_date or end_date:
                data = self._filter_by_date(data, start_date, end_date)
            
            # Cache the result
            self.data_cache[cache_key] = data.copy()
            
            self.logger.info(f"Loaded {len(data)} records for {symbol} from {start_date} to {end_date}")
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to load data for {symbol}: {e}")
            raise
    
    def _load_csv_data(self, symbol: str, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """Load data from CSV files"""
        # Try different common file naming patterns
        possible_files = [
            f"{symbol}.csv",
            f"{symbol}_daily.csv",
            f"{symbol.replace('.', '_')}.csv",
            f"data_{symbol}.csv"
        ]
        
        data_file = None
        for filename in possible_files:
            file_path = self.data_dir / filename
            if file_path.exists():
                data_file = file_path
                break
        
        if data_file is None:
            # Create sample data if no file exists
            self.logger.warning(f"No data file found for {symbol}, creating sample data")
            return self._create_sample_data(symbol, start_date, end_date)
        
        # Load CSV with flexible parsing
        try:
            data = pd.read_csv(data_file)
            self.logger.debug(f"Loaded CSV data from {data_file}")
            return data
        except Exception as e:
            self.logger.error(f"Failed to load CSV file {data_file}: {e}")
            raise
    
    def _load_akshare_data(self, symbol: str, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """通过akshare获取A股/ETF历史行情"""
        if not AKSHARE_AVAILABLE:
            raise ImportError("akshare is not installed. Please install akshare to use this feature.")
        # 支持股票和ETF，自动判断symbol格式
        if symbol.endswith('.SZ') or symbol.endswith('.SH'):
            code = symbol.replace('.SZ', '').replace('.SH', '')
            try:
                df = ak.stock_zh_a_hist(symbol=code, period="daily", start_date=start_date, end_date=end_date, adjust="qfq")
            except Exception as e:
                self.logger.error(f"akshare获取A股数据失败: {e}")
                raise
            # akshare返回列：日期,开盘,收盘,最高,最低,成交量,...
            df = df.rename(columns={
                '日期': 'date', '开盘': 'open', '收盘': 'close', '最高': 'high', '最低': 'low', '成交量': 'volume'
            })
            df['symbol'] = symbol
            return df[['date', 'symbol', 'open', 'high', 'low', 'close', 'volume']]
        else:
            # ETF等其他品种可扩展
            raise ValueError(f"暂不支持该symbol的akshare行情: {symbol}")
    
    def _load_tushare_data(self, symbol: str, start_date: Optional[str], end_date: Optional[str], tushare_token: Optional[str]) -> pd.DataFrame:
        """通过tushare获取A股/ETF历史行情"""
        if not TUSHARE_AVAILABLE:
            raise ImportError("tushare is not installed. Please install tushare to use this feature.")
        token = tushare_token or os.environ.get('TUSHARE_TOKEN')
        if not token:
            raise ValueError("tushare token未配置，请通过参数或环境变量TUSHARE_TOKEN传入")
        ts.set_token(token)
        pro = ts.pro_api()
        if symbol.endswith('.SZ') or symbol.endswith('.SH'):
            ts_code = symbol.replace('.SZ', '.SZ').replace('.SH', '.SH')
            try:
                df = pro.daily(ts_code=ts_code, start_date=start_date.replace('-', ''), end_date=end_date.replace('-', ''))
            except Exception as e:
                self.logger.error(f"tushare获取A股数据失败: {e}")
                raise
            # tushare返回列：ts_code,trade_date,open,high,low,close,vol,...
            df = df.rename(columns={
                'trade_date': 'date', 'vol': 'volume', 'ts_code': 'symbol'
            })
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            return df[['date', 'symbol', 'open', 'high', 'low', 'close', 'volume']]
        else:
            raise ValueError(f"暂不支持该symbol的tushare行情: {symbol}")
    
    def _load_database_data(self, symbol: str, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """Load data from database (placeholder implementation)"""
        self.logger.warning("Database data loading not implemented, using sample data")
        return self._create_sample_data(symbol, start_date, end_date)
    
    def _load_api_data(self, symbol: str, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """Load data from API (placeholder implementation)"""
        self.logger.warning("API data loading not implemented, using sample data")
        return self._create_sample_data(symbol, start_date, end_date)
    
    def _validate_and_standardize(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """Validate and standardize data format"""
        df = data.copy()
        
        # Standardize column names
        df.columns = df.columns.str.strip()
        for old_name, new_name in self.column_mapping.items():
            if old_name in df.columns:
                df = df.rename(columns={old_name: new_name})
        
        # Ensure required columns exist
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"Missing required columns for {symbol}: {missing_columns}")
        
        # Add symbol column if not present
        if 'symbol' not in df.columns:
            df['symbol'] = symbol
        
        # Parse date column
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        elif df.index.name == 'Date' or 'date' in str(df.index.name).lower():
            df['date'] = pd.to_datetime(df.index)
            df = df.reset_index(drop=True)
        else:
            # Create synthetic date column if missing
            df['date'] = pd.date_range(start='2020-01-01', periods=len(df), freq='D')
        
        # Validate data quality
        df = self._validate_data_quality(df, symbol)
        
        # Sort by date
        df = df.sort_values('date').reset_index(drop=True)
        
        return df
    
    def _validate_data_quality(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """Validate and clean data quality issues"""
        df = data.copy()
        original_len = len(df)
        
        # Remove rows with missing OHLC data
        required_cols = ['open', 'high', 'low', 'close']
        df = df.dropna(subset=required_cols)
        
        # Remove rows with invalid prices (negative or zero)
        for col in required_cols:
            df = df[df[col] > 0]
        
        # Check price consistency (high >= low, etc.)
        df = df[df['high'] >= df['low']]
        df = df[df['high'] >= df['open']]
        df = df[df['high'] >= df['close']]
        df = df[df['low'] <= df['open']]
        df = df[df['low'] <= df['close']]
        
        # Handle missing volume (set to 0 if missing)
        if 'volume' in df.columns:
            df['volume'] = df['volume'].fillna(0)
            df['volume'] = df['volume'].clip(lower=0)
        
        # Remove extreme outliers (price changes > 50% in one day)
        df['price_change'] = df['close'].pct_change()
        df = df[abs(df['price_change']) <= 0.5]
        df = df.drop('price_change', axis=1)
        
        removed_count = original_len - len(df)
        if removed_count > 0:
            self.logger.warning(f"Removed {removed_count} invalid records for {symbol}")
        
        return df
    
    def _filter_by_date(self, data: pd.DataFrame, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """Filter data by date range"""
        df = data.copy()
        
        if 'date' not in df.columns:
            return df
        
        if start_date:
            start_dt = pd.to_datetime(start_date)
            df = df[df['date'] >= start_dt]
        
        if end_date:
            end_dt = pd.to_datetime(end_date)
            df = df[df['date'] <= end_dt]
        
        return df
    
    def _create_sample_data(self, symbol: str, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """Create sample data for testing when real data is not available"""
        # Determine date range
        if start_date:
            start_dt = pd.to_datetime(start_date)
        else:
            start_dt = pd.to_datetime('2020-01-01')
        
        if end_date:
            end_dt = pd.to_datetime(end_date)
        else:
            end_dt = pd.to_datetime('2023-12-31')
        
        # Generate date range
        dates = pd.date_range(start=start_dt, end=end_dt, freq='D')
        dates = dates[dates.weekday < 5]  # Remove weekends
        
        # Generate synthetic price data with realistic patterns
        np.random.seed(42)  # For reproducible results
        
        n_days = len(dates)
        initial_price = 100.0
        
        # Generate returns with some trending and mean reversion
        trend = np.linspace(0, 0.2, n_days)  # 20% upward trend over period
        noise = np.random.normal(0, 0.02, n_days)  # 2% daily volatility
        returns = trend / n_days + noise
        
        # Generate price series
        prices = [initial_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # Generate OHLC data
        data_records = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            # Generate intraday range
            daily_range = abs(np.random.normal(0, 0.01))
            
            open_price = close * (1 + np.random.uniform(-0.005, 0.005))
            high = max(open_price, close) * (1 + daily_range)
            low = min(open_price, close) * (1 - daily_range)
            
            # Generate volume (log-normal distribution)
            volume = int(np.random.lognormal(15, 1))  # Around 3M average volume
            
            data_records.append({
                'date': date,
                'symbol': symbol,
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close, 2),
                'volume': volume
            })
        
        df = pd.DataFrame(data_records)
        
        self.logger.info(f"Created {len(df)} days of sample data for {symbol}")
        return df
    
    def get_available_symbols(self) -> List[str]:
        """Get list of available symbols in the data directory"""
        symbols = []
        
        for file_path in self.data_dir.glob("*.csv"):
            # Extract symbol from filename
            filename = file_path.stem
            
            # Try different naming patterns
            if filename.endswith('_daily'):
                symbol = filename.replace('_daily', '')
            elif filename.startswith('data_'):
                symbol = filename.replace('data_', '')
            else:
                symbol = filename
            
            symbols.append(symbol)
        
        return sorted(symbols)
    
    def clear_cache(self):
        """Clear the data cache"""
        self.data_cache.clear()
        self.logger.info("Data cache cleared")
    
    def save_sample_data(self, symbol: str, start_date: str = "2020-01-01", end_date: str = "2023-12-31"):
        """Save sample data to CSV file for testing"""
        data = self._create_sample_data(symbol, start_date, end_date)
        
        filename = f"{symbol}.csv"
        file_path = self.data_dir / filename
        
        data.to_csv(file_path, index=False)
        self.logger.info(f"Saved sample data for {symbol} to {file_path}")
        
        return file_path 