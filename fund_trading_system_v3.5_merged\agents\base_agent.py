"""
Base Agent Interface for Trading System v3.5

Defines the standardized interface that all trading agents must implement.
This ensures consistent interaction between agents and the coordination manager.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any, Optional, List
import numpy as np


@dataclass
class MarketData:
    """Standardized market data structure for all agents"""
    symbol: str
    timestamp: datetime
    
    # Price data
    open: float
    high: float
    low: float
    close: float
    volume: float
    
    # Technical indicators (20-dimensional)
    technical_indicators: Dict[str, float]
    
    # CZSC structure data (10-dimensional) 
    czsc_structure: Dict[str, Any]
    
    # Additional context
    news_data: Optional[List[Dict[str, Any]]] = None
    macro_data: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = None
    
    def get_price_change(self) -> float:
        """Calculate price change percentage"""
        if self.open > 0:
            return (self.close - self.open) / self.open
        return 0.0
    
    def get_volatility(self) -> float:
        """Calculate simple volatility based on high-low range"""
        if self.close > 0:
            return (self.high - self.low) / self.close
        return 0.0
    
    def to_feature_vector(self) -> np.ndarray:
        """Convert MarketData to 50-dimensional feature vector"""
        # Technical indicators (20-dim)
        tech_features = np.zeros(20)
        if self.technical_indicators:
            for i, (key, value) in enumerate(list(self.technical_indicators.items())[:20]):
                if i < 20:
                    tech_features[i] = float(value) if value is not None else 0.0
        
        # CZSC structure (10-dim) 
        czsc_features = np.zeros(10)
        if self.czsc_structure:
            for i, (key, value) in enumerate(list(self.czsc_structure.items())[:10]):
                if i < 10:
                    czsc_features[i] = float(value) if value is not None else 0.0
        
        # LLM context features (20-dim) - basic price/volume derived
        llm_features = np.zeros(20)
        price_features = [
            self.get_price_change(),
            self.get_volatility(),
            np.log(self.volume) / 20 if self.volume > 0 else 0,  # Normalized volume
            (self.close - self.low) / (self.high - self.low) if self.high > self.low else 0.5,  # Price position
        ]
        
        for i, feature in enumerate(price_features[:20]):
            llm_features[i] = feature
        
        # Combine all features
        return np.concatenate([tech_features, czsc_features, llm_features])


@dataclass 
class TradingSignal:
    """Standardized trading signal from agents"""
    agent_type: str  # 'rl', 'llm', or 'czsc'
    action: str      # 'buy', 'sell', or 'hold'
    strength: float  # Signal strength 0.0 to 1.0
    confidence: float # Confidence level 0.0 to 1.0
    timestamp: datetime
    reasoning: str   # Human-readable explanation
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        """Validate signal parameters"""
        assert self.action in ['buy', 'sell', 'hold'], f"Invalid action: {self.action}"
        assert 0.0 <= self.strength <= 1.0, f"Strength must be 0-1: {self.strength}"
        assert 0.0 <= self.confidence <= 1.0, f"Confidence must be 0-1: {self.confidence}"


class BaseAgent(ABC):
    """
    Abstract base class for all trading agents in the system.
    
    All agents (RL, LLM, CZSC) must implement this interface to ensure
    consistent interaction with the Agent Collaboration Manager.
    """
    
    def __init__(self, name: str):
        """
        Initialize the base agent.
        
        Args:
            name: Human-readable name for the agent
        """
        self.name = name
        self.agent_type = self.__class__.__name__.lower().replace('agent', '')
        self.is_initialized = False
        self.performance_metrics = {
            'total_signals': 0,
            'successful_signals': 0,
            'accuracy_rate': 0.0,
            'last_update': datetime.now()
        }
    
    @abstractmethod
    def generate_signal(self, data: MarketData) -> TradingSignal:
        """
        Generate a trading signal based on market data.
        
        Args:
            data: Standardized market data structure
            
        Returns:
            TradingSignal: Standardized signal with action, confidence, and reasoning
        """
        pass
    
    @abstractmethod 
    def get_confidence(self) -> float:
        """
        Get the current confidence level of the agent.
        
        Returns:
            float: Confidence level between 0.0 and 1.0
        """
        pass
    
    @abstractmethod
    def get_explanation(self) -> str:
        """
        Get a human-readable explanation of the agent's current state/reasoning.
        
        Returns:
            str: Explanation of current analysis and decision factors
        """
        pass
    
    def initialize(self) -> bool:
        """
        Initialize the agent with required resources.
        Override in subclasses for specific initialization logic.
        
        Returns:
            bool: True if initialization successful
        """
        self.is_initialized = True
        return True
    
    def update_performance(self, signal_success: bool):
        """
        Update agent performance metrics.
        
        Args:
            signal_success: Whether the signal was successful
        """
        self.performance_metrics['total_signals'] += 1
        if signal_success:
            self.performance_metrics['successful_signals'] += 1
        
        total = self.performance_metrics['total_signals']
        successful = self.performance_metrics['successful_signals']
        self.performance_metrics['accuracy_rate'] = successful / total if total > 0 else 0.0
        self.performance_metrics['last_update'] = datetime.now()
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current agent status and performance metrics.
        
        Returns:
            Dict containing agent status and metrics
        """
        return {
            'name': self.name,
            'type': self.agent_type,
            'initialized': self.is_initialized,
            'performance': self.performance_metrics.copy()
        } 