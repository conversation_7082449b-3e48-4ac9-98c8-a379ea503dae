"""
RL Model Manager

Handles model lifecycle management including training, saving, loading,
versioning, and performance monitoring for RL trading agents.
"""

import logging
import numpy as np
import pandas as pd
import json
import pickle
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
import shutil

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    # Create minimal torch-like interface for compatibility
    class torch:
        class nn:
            class Module:
                def __init__(self):
                    pass
                def forward(self, x):
                    return x
                def parameters(self):
                    return []
                def load_state_dict(self, state_dict):
                    pass
                def state_dict(self):
                    return {}
                def eval(self):
                    pass
                def train(self):
                    pass

from core.config import TradingConfig
from rl_infrastructure.trading_environment import TradingEnvironment


@dataclass
class ModelMetadata:
    """Metadata for RL model versioning"""
    model_id: str
    version: str
    created_at: datetime
    algorithm: str
    architecture: Dict[str, Any]
    training_config: Dict[str, Any]
    performance_metrics: Dict[str, float]
    data_hash: str
    model_hash: str
    description: str = ""
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []


@dataclass 
class TrainingResult:
    """Results from model training session"""
    model_id: str
    version: str
    episode_rewards: List[float]
    episode_lengths: List[int]
    training_loss: List[float]
    validation_metrics: Dict[str, float]
    training_time: float
    total_episodes: int
    final_performance: Dict[str, float]
    convergence_info: Dict[str, Any]


class SimpleDQN(torch.nn.Module if TORCH_AVAILABLE else object):
    """Simple Deep Q-Network for trading"""
    
    def __init__(self, state_dim: int = 50, action_dim: int = 3, hidden_dims: List[int] = None):
        if TORCH_AVAILABLE:
            super(SimpleDQN, self).__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.hidden_dims = hidden_dims or [256, 256, 128]
        
        if TORCH_AVAILABLE:
            # Build network layers
            layers = []
            prev_dim = state_dim
            
            for hidden_dim in self.hidden_dims:
                layers.extend([
                    nn.Linear(prev_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Dropout(0.1)
                ])
                prev_dim = hidden_dim
            
            # Output layer
            layers.append(nn.Linear(prev_dim, action_dim))
            
            self.network = nn.Sequential(*layers)
        else:
            # Fallback implementation
            self.network = None
    
    def forward(self, x):
        if TORCH_AVAILABLE and self.network is not None:
            return self.network(x)
        else:
            # Simple fallback: random actions
            batch_size = x.shape[0] if hasattr(x, 'shape') else 1
            return np.random.randn(batch_size, self.action_dim)
    
    def predict(self, state: np.ndarray) -> int:
        """Predict action for given state"""
        if TORCH_AVAILABLE and self.network is not None:
            self.eval()
            with torch.no_grad():
                if len(state.shape) == 1:
                    state = state.reshape(1, -1)
                state_tensor = torch.FloatTensor(state)
                q_values = self.forward(state_tensor)
                action = torch.argmax(q_values, dim=1).item()
                return action
        else:
            # Fallback: random action
            return np.random.randint(0, self.action_dim)


class RLModelManager:
    """
    Comprehensive RL Model Management System
    
    Handles model lifecycle including training, evaluation, versioning,
    and deployment for reinforcement learning trading agents.
    """
    
    def __init__(self, 
                 config: TradingConfig,
                 models_dir: str = "models",
                 enable_versioning: bool = True):
        """
        Initialize RL model manager.
        
        Args:
            config: Trading system configuration
            models_dir: Directory for storing models
            enable_versioning: Whether to enable model versioning
        """
        self.logger = logging.getLogger("RLModelManager")
        self.config = config
        self.models_dir = Path(models_dir)
        self.enable_versioning = enable_versioning
        
        # Create directories
        self.models_dir.mkdir(parents=True, exist_ok=True)
        (self.models_dir / "checkpoints").mkdir(exist_ok=True)
        (self.models_dir / "metadata").mkdir(exist_ok=True)
        (self.models_dir / "training_logs").mkdir(exist_ok=True)
        
        # Model registry
        self.model_registry = {}
        self.current_model = None
        
        # Training state
        self.training_history = []
        self.performance_tracker = {}
        
        # Load existing registry
        self._load_model_registry()
        
        self.logger.info(f"RL Model Manager initialized with models directory: {self.models_dir}")
    
    def create_model(self, 
                     algorithm: str = "DQN",
                     architecture_config: Optional[Dict[str, Any]] = None) -> SimpleDQN:
        """
        Create new RL model with specified architecture.
        
        Args:
            algorithm: Algorithm name (DQN, PPO, SAC, etc.)
            architecture_config: Model architecture configuration
            
        Returns:
            Initialized model
        """
        if architecture_config is None:
            architecture_config = {
                'state_dim': 50,
                'action_dim': 3,
                'hidden_dims': [256, 256, 128]
            }
        
        if algorithm == "DQN":
            model = SimpleDQN(
                state_dim=architecture_config.get('state_dim', 50),
                action_dim=architecture_config.get('action_dim', 3),
                hidden_dims=architecture_config.get('hidden_dims', [256, 256, 128])
            )
        else:
            # Fallback to DQN for unsupported algorithms
            self.logger.warning(f"Algorithm {algorithm} not implemented, using DQN")
            model = SimpleDQN(**architecture_config)
        
        self.logger.info(f"Created {algorithm} model with architecture: {architecture_config}")
        return model
    
    def train_model(self,
                    model: SimpleDQN,
                    training_env: TradingEnvironment,
                    validation_env: Optional[TradingEnvironment] = None,
                    episodes: int = 1000,
                    learning_rate: float = 0.001,
                    epsilon_start: float = 1.0,
                    epsilon_end: float = 0.01,
                    epsilon_decay: float = 0.995,
                    batch_size: int = 32,
                    save_frequency: int = 100) -> TrainingResult:
        """
        Train RL model using specified environment.
        
        Args:
            model: Model to train
            training_env: Training environment
            validation_env: Optional validation environment  
            episodes: Number of training episodes
            learning_rate: Learning rate for optimization
            epsilon_start: Initial exploration rate
            epsilon_end: Final exploration rate
            epsilon_decay: Exploration decay rate
            batch_size: Batch size for training
            save_frequency: How often to save checkpoints
            
        Returns:
            Training results
        """
        self.logger.info(f"Starting model training for {episodes} episodes")
        
        # Initialize training tracking
        episode_rewards = []
        episode_lengths = []
        training_losses = []
        epsilon = epsilon_start
        
        start_time = datetime.now()
        
        # Simple training loop (DQN-style)
        for episode in range(episodes):
            state = training_env.reset()
            total_reward = 0
            steps = 0
            done = False
            
            while not done:
                # Epsilon-greedy action selection
                if np.random.random() < epsilon:
                    action = np.random.randint(0, 3)  # Random action
                else:
                    action = model.predict(state)
                
                # Execute action
                next_state, reward, done, info = training_env.step(action)
                total_reward += reward
                steps += 1
                
                # Update state
                state = next_state
                
                # Simple loss simulation (placeholder for actual training)
                if TORCH_AVAILABLE:
                    training_losses.append(abs(reward) * 0.1)  # Placeholder loss
            
            # Record episode results
            episode_rewards.append(total_reward)
            episode_lengths.append(steps)
            
            # Decay exploration
            epsilon = max(epsilon_end, epsilon * epsilon_decay)
            
            # Progress logging
            if episode % 100 == 0:
                avg_reward = np.mean(episode_rewards[-100:])
                self.logger.info(f"Episode {episode}/{episodes}, "
                               f"Avg Reward: {avg_reward:.2f}, "
                               f"Epsilon: {epsilon:.3f}")
            
            # Save checkpoint
            if episode % save_frequency == 0 and episode > 0:
                checkpoint_path = self.models_dir / "checkpoints" / f"episode_{episode}.pt"
                self._save_checkpoint(model, checkpoint_path, episode, total_reward)
        
        training_time = (datetime.now() - start_time).total_seconds()
        
        # Validation
        validation_metrics = {}
        if validation_env is not None:
            validation_metrics = self._validate_model(model, validation_env)
        
        # Create training result
        model_id = self._generate_model_id()
        version = "1.0.0"
        
        training_result = TrainingResult(
            model_id=model_id,
            version=version,
            episode_rewards=episode_rewards,
            episode_lengths=episode_lengths,
            training_loss=training_losses,
            validation_metrics=validation_metrics,
            training_time=training_time,
            total_episodes=episodes,
            final_performance=training_env.get_portfolio_stats(),
            convergence_info={
                'final_epsilon': epsilon,
                'avg_final_reward': np.mean(episode_rewards[-100:]),
                'best_episode_reward': max(episode_rewards),
                'convergence_episode': np.argmax(episode_rewards)
            }
        )
        
        # Save training results
        self.training_history.append(training_result)
        self._save_training_results(training_result)
        
        self.logger.info(f"Training completed in {training_time:.2f} seconds")
        self.logger.info(f"Final performance: {training_result.final_performance}")
        
        return training_result
    
    def save_model(self,
                   model: SimpleDQN,
                   model_id: str,
                   version: str,
                   metadata: Optional[Dict[str, Any]] = None,
                   description: str = "") -> str:
        """
        Save model with metadata and versioning.
        
        Args:
            model: Model to save
            model_id: Unique model identifier
            version: Model version
            metadata: Additional metadata
            description: Model description
            
        Returns:
            Path to saved model
        """
        # Create model directory
        model_dir = self.models_dir / model_id / version
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # Save model state
        model_path = model_dir / "model.pt"
        if TORCH_AVAILABLE:
            torch.save(model.state_dict(), model_path)
        else:
            # Fallback: save architecture info
            with open(model_path, 'w') as f:
                json.dump({
                    'state_dim': model.state_dim,
                    'action_dim': model.action_dim,
                    'hidden_dims': model.hidden_dims
                }, f)
        
        # Generate metadata
        model_metadata = ModelMetadata(
            model_id=model_id,
            version=version,
            created_at=datetime.now(),
            algorithm="DQN",
            architecture={
                'state_dim': model.state_dim,
                'action_dim': model.action_dim,
                'hidden_dims': model.hidden_dims
            },
            training_config=asdict(self.config.rl_config),
            performance_metrics=self.performance_tracker.get(model_id, {}),
            data_hash=self._generate_data_hash(),
            model_hash=self._generate_model_hash(model),
            description=description
        )
        
        # Save metadata
        metadata_path = model_dir / "metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(asdict(model_metadata), f, indent=2, default=str)
        
        # Update registry
        if model_id not in self.model_registry:
            self.model_registry[model_id] = {}
        self.model_registry[model_id][version] = {
            'path': str(model_path),
            'metadata_path': str(metadata_path),
            'created_at': model_metadata.created_at.isoformat(),
            'performance': model_metadata.performance_metrics
        }
        
        self._save_model_registry()
        
        self.logger.info(f"Model saved: {model_id} v{version} at {model_path}")
        return str(model_path)
    
    def load_model(self, model_id: str, version: str = "latest") -> Tuple[SimpleDQN, ModelMetadata]:
        """
        Load model with metadata.
        
        Args:
            model_id: Model identifier
            version: Model version ("latest" for most recent)
            
        Returns:
            Loaded model and metadata
        """
        if model_id not in self.model_registry:
            raise ValueError(f"Model {model_id} not found in registry")
        
        # Get version
        if version == "latest":
            versions = list(self.model_registry[model_id].keys())
            version = max(versions)  # Assumes semantic versioning
        
        if version not in self.model_registry[model_id]:
            raise ValueError(f"Version {version} not found for model {model_id}")
        
        # Load metadata
        metadata_path = self.model_registry[model_id][version]['metadata_path']
        with open(metadata_path, 'r') as f:
            metadata_dict = json.load(f)
        
        # Convert back to ModelMetadata
        metadata_dict['created_at'] = datetime.fromisoformat(metadata_dict['created_at'])
        metadata = ModelMetadata(**metadata_dict)
        
        # Create and load model
        model = self.create_model(
            algorithm=metadata.algorithm,
            architecture_config=metadata.architecture
        )
        
        model_path = self.model_registry[model_id][version]['path']
        
        if TORCH_AVAILABLE and Path(model_path).suffix == '.pt':
            model.load_state_dict(torch.load(model_path))
        
        self.current_model = model
        
        self.logger.info(f"Model loaded: {model_id} v{version}")
        return model, metadata
    
    def _validate_model(self, model: SimpleDQN, validation_env: TradingEnvironment) -> Dict[str, float]:
        """Validate model performance on validation environment"""
        self.logger.info("Running model validation...")
        
        state = validation_env.reset()
        total_reward = 0
        steps = 0
        done = False
        
        while not done:
            action = model.predict(state)
            state, reward, done, info = validation_env.step(action)
            total_reward += reward
            steps += 1
        
        validation_stats = validation_env.get_portfolio_stats()
        validation_stats['total_reward'] = total_reward
        validation_stats['steps'] = steps
        
        self.logger.info(f"Validation completed: {validation_stats}")
        return validation_stats
    
    def _save_checkpoint(self, model: SimpleDQN, path: Path, episode: int, reward: float):
        """Save training checkpoint"""
        checkpoint_data = {
            'episode': episode,
            'reward': reward,
            'timestamp': datetime.now().isoformat()
        }
        
        if TORCH_AVAILABLE:
            checkpoint_data['model_state'] = model.state_dict()
            torch.save(checkpoint_data, path)
        else:
            with open(path.with_suffix('.json'), 'w') as f:
                json.dump(checkpoint_data, f, indent=2)
    
    def _generate_model_id(self) -> str:
        """Generate unique model ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"rl_model_{timestamp}"
    
    def _generate_data_hash(self) -> str:
        """Generate hash for training data"""
        # Placeholder implementation
        return hashlib.md5(str(datetime.now()).encode()).hexdigest()[:8]
    
    def _generate_model_hash(self, model: SimpleDQN) -> str:
        """Generate hash for model architecture"""
        model_info = f"{model.state_dim}_{model.action_dim}_{model.hidden_dims}"
        return hashlib.md5(model_info.encode()).hexdigest()[:8]
    
    def _save_training_results(self, result: TrainingResult):
        """Save training results to file"""
        results_path = self.models_dir / "training_logs" / f"{result.model_id}_{result.version}.json"
        
        with open(results_path, 'w') as f:
            json.dump(asdict(result), f, indent=2, default=str)
    
    def _load_model_registry(self):
        """Load model registry from disk"""
        registry_path = self.models_dir / "model_registry.json"
        
        if registry_path.exists():
            with open(registry_path, 'r') as f:
                self.model_registry = json.load(f)
            self.logger.info(f"Loaded model registry with {len(self.model_registry)} models")
        else:
            self.model_registry = {}
    
    def _save_model_registry(self):
        """Save model registry to disk"""
        registry_path = self.models_dir / "model_registry.json"
        
        with open(registry_path, 'w') as f:
            json.dump(self.model_registry, f, indent=2, default=str)
    
    def list_models(self) -> Dict[str, List[str]]:
        """List all available models and versions"""
        return {model_id: list(versions.keys()) 
                for model_id, versions in self.model_registry.items()}
    
    def get_model_performance(self, model_id: str, version: str = "latest") -> Dict[str, float]:
        """Get performance metrics for specific model version"""
        if model_id not in self.model_registry:
            return {}
        
        if version == "latest":
            versions = list(self.model_registry[model_id].keys())
            version = max(versions)
        
        return self.model_registry[model_id][version].get('performance', {})
    
    def compare_models(self, model_specs: List[Tuple[str, str]]) -> pd.DataFrame:
        """Compare performance of multiple models"""
        comparison_data = []
        
        for model_id, version in model_specs:
            perf = self.get_model_performance(model_id, version)
            perf['model_id'] = model_id
            perf['version'] = version
            comparison_data.append(perf)
        
        return pd.DataFrame(comparison_data)
    
    def cleanup_old_models(self, keep_versions: int = 5):
        """Clean up old model versions to save space"""
        for model_id, versions in self.model_registry.items():
            if len(versions) > keep_versions:
                # Sort by creation date and keep only recent versions
                sorted_versions = sorted(
                    versions.items(),
                    key=lambda x: x[1]['created_at'],
                    reverse=True
                )
                
                for version, info in sorted_versions[keep_versions:]:
                    # Remove files
                    model_dir = Path(info['path']).parent
                    if model_dir.exists():
                        shutil.rmtree(model_dir)
                    
                    # Remove from registry
                    del self.model_registry[model_id][version]
                    
                    self.logger.info(f"Cleaned up old model: {model_id} v{version}")
        
        self._save_model_registry()
    
    def export_model(self, model_id: str, version: str, export_path: str):
        """Export model for deployment"""
        model, metadata = self.load_model(model_id, version)
        
        export_dir = Path(export_path)
        export_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy model files
        model_dir = self.models_dir / model_id / version
        shutil.copytree(model_dir, export_dir / f"{model_id}_{version}")
        
        # Create deployment manifest
        manifest = {
            'model_id': model_id,
            'version': version,
            'exported_at': datetime.now().isoformat(),
            'metadata': asdict(metadata)
        }
        
        with open(export_dir / "deployment_manifest.json", 'w') as f:
            json.dump(manifest, f, indent=2, default=str)
        
        self.logger.info(f"Model exported to {export_path}") 