"""
Enhanced Large Language Model Agent Implementation

This agent uses multiple LLM providers for sentiment analysis, market insights,
and contextual understanding of market conditions.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
import numpy as np

from agents.base_agent import BaseAgent, TradingSignal, MarketData
from .providers.base_provider import LLMProviderBase, MockLLMProvider


class LLMAgent(BaseAgent):
    """Enhanced LLM Agent with multiple provider support"""

    def __init__(self, name: str = "LLM", providers: Optional[List[LLMProviderBase]] = None):
        super().__init__(name)
        self.agent_type = "llm"

        # Initialize providers
        self.providers = providers or [MockLLMProvider()]
        self.primary_provider = self.providers[0] if self.providers else MockLLMProvider()
        self.fallback_providers = self.providers[1:] if len(self.providers) > 1 else []

        # Initialize providers
        for provider in self.providers:
            if not provider.initialize():
                self.logger.warning(f"Failed to initialize provider: {provider.config.provider_name}")

        self.logger.info(f"LLM Agent initialized with {len(self.providers)} providers")
        self.is_initialized = True

    def generate_signal(self, data: MarketData) -> TradingSignal:
        """Generate trading signal using LLM analysis"""
        try:
            # Analyze sentiment from news data
            sentiment_response = self._analyze_sentiment(data)

            # Generate market insights
            insight_response = self._generate_insights(data)

            # Combine responses to create trading signal
            combined_confidence = (sentiment_response.confidence + insight_response.confidence) / 2
            combined_scores = self._combine_sentiment_scores(
                sentiment_response.sentiment_scores,
                insight_response.sentiment_scores
            )

            # Determine action based on sentiment
            action = self._determine_action(combined_scores)
            strength = self._calculate_strength(combined_scores, combined_confidence)

            reasoning = f"LLM Analysis - Sentiment: {sentiment_response.reasoning[:100]}... " \
                       f"Insight: {insight_response.reasoning[:100]}..."

            return TradingSignal(
                agent_type=self.agent_type,
                action=action,
                strength=strength,
                confidence=combined_confidence,
                timestamp=datetime.now(),
                reasoning=reasoning,
                metadata={
                    'sentiment_scores': combined_scores,
                    'sentiment_provider': sentiment_response.provider,
                    'insight_provider': insight_response.provider,
                    'providers_used': len(self.providers)
                }
            )

        except Exception as e:
            self.logger.error(f"Error generating LLM signal: {e}")
            return TradingSignal(
                agent_type=self.agent_type,
                action="hold",
                strength=0.0,
                confidence=0.1,
                timestamp=datetime.now(),
                reasoning=f"LLM analysis failed: {str(e)}",
                metadata={'error': True}
            )

    def get_confidence(self) -> float:
        """Get current confidence level"""
        if not self.providers:
            return 0.1

        # Calculate confidence based on provider availability
        available_providers = sum(1 for p in self.providers if p.is_available())
        base_confidence = available_providers / len(self.providers)

        # Adjust based on recent performance
        recent_accuracy = self.performance_metrics.get('accuracy_rate', 0.5)
        return min(0.9, base_confidence * 0.7 + recent_accuracy * 0.3)

    def _analyze_sentiment(self, data: MarketData):
        """Analyze sentiment from market data"""
        # Prepare text for analysis
        text_data = self._prepare_text_data(data)

        # Try primary provider first
        for provider in self.providers:
            if provider.is_available():
                try:
                    return provider.analyze_market_sentiment(text_data, {
                        'symbol': data.symbol,
                        'price_change': data.get_price_change(),
                        'volume': data.volume
                    })
                except Exception as e:
                    self.logger.warning(f"Provider {provider.config.provider_name} failed: {e}")
                    continue

        # Fallback to mock response
        mock_provider = MockLLMProvider()
        mock_provider.initialize()
        return mock_provider.analyze_market_sentiment(text_data)

    def _generate_insights(self, data: MarketData):
        """Generate market insights from data"""
        market_data_dict = {
            'symbol': data.symbol,
            'price_change': data.get_price_change(),
            'volume': data.volume,
            'technical_indicators': data.technical_indicators,
            'news_data': data.news_data
        }

        # Try primary provider first
        for provider in self.providers:
            if provider.is_available():
                try:
                    return provider.generate_market_insight(market_data_dict)
                except Exception as e:
                    self.logger.warning(f"Provider {provider.config.provider_name} failed: {e}")
                    continue

        # Fallback to mock response
        mock_provider = MockLLMProvider()
        mock_provider.initialize()
        return mock_provider.generate_market_insight(market_data_dict)

    def _prepare_text_data(self, data: MarketData) -> str:
        """Prepare text data for LLM analysis"""
        text_parts = []

        # Add basic market info
        text_parts.append(f"Market data for {data.symbol}:")
        text_parts.append(f"Price change: {data.get_price_change():.2%}")
        text_parts.append(f"Volume: {data.volume:,}")

        # Add news data if available
        if data.news_data:
            text_parts.append("Recent news:")
            for news_item in data.news_data[:3]:  # Limit to 3 most recent
                if isinstance(news_item, dict):
                    title = news_item.get('title', '')
                    content = news_item.get('content', '')
                    text_parts.append(f"- {title}: {content[:200]}...")
                else:
                    text_parts.append(f"- {str(news_item)[:200]}...")

        # Add technical indicators context
        if data.technical_indicators:
            text_parts.append("Technical indicators suggest:")
            rsi = data.technical_indicators.get('rsi', 50)
            if rsi > 70:
                text_parts.append("- Overbought conditions (RSI > 70)")
            elif rsi < 30:
                text_parts.append("- Oversold conditions (RSI < 30)")

            macd = data.technical_indicators.get('macd', 0)
            if macd > 0:
                text_parts.append("- Bullish momentum (MACD positive)")
            elif macd < 0:
                text_parts.append("- Bearish momentum (MACD negative)")

        return "\n".join(text_parts)

    def _combine_sentiment_scores(self, scores1: dict, scores2: dict) -> dict:
        """Combine sentiment scores from multiple sources"""
        combined = {}
        all_keys = set(scores1.keys()) | set(scores2.keys())

        for key in all_keys:
            val1 = scores1.get(key, 0.0)
            val2 = scores2.get(key, 0.0)
            combined[key] = (val1 + val2) / 2

        return combined

    def _determine_action(self, sentiment_scores: dict) -> str:
        """Determine trading action based on sentiment scores"""
        positive = sentiment_scores.get('positive', 0.0)
        negative = sentiment_scores.get('negative', 0.0)
        neutral = sentiment_scores.get('neutral', 0.0)

        if positive > negative + 0.2 and positive > 0.6:
            return "buy"
        elif negative > positive + 0.2 and negative > 0.6:
            return "sell"
        else:
            return "hold"

    def _calculate_strength(self, sentiment_scores: dict, confidence: float) -> float:
        """Calculate signal strength based on sentiment and confidence"""
        positive = sentiment_scores.get('positive', 0.0)
        negative = sentiment_scores.get('negative', 0.0)

        # Calculate sentiment strength
        sentiment_strength = abs(positive - negative)

        # Combine with confidence
        return min(1.0, sentiment_strength * confidence)

    def add_provider(self, provider: LLMProviderBase):
        """Add a new LLM provider"""
        if provider.initialize():
            self.providers.append(provider)
            self.logger.info(f"Added provider: {provider.config.provider_name}")
        else:
            self.logger.error(f"Failed to add provider: {provider.config.provider_name}")

    def get_provider_stats(self) -> dict:
        """Get statistics for all providers"""
        stats = {}
        for provider in self.providers:
            stats[provider.config.provider_name] = provider.get_stats()
        return stats